const uWS = require('uWebSockets.js');
const speech = require('@google-cloud/speech');
const textToSpeech = require('@google-cloud/text-to-speech');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuración desde variables de entorno
const config = {
  port: process.env.PORT || 8080,
  ia: {
    apiUrl: process.env.VITE_IA_API_URL || "https://dev.dl2discovery.org/llm-api/v1/",
    apiKey: process.env.VITE_IA_API_KEY || "9dcd0147-11e2-4e9e-aaf3-05e1498ce828",
    presets: {
      genCharBot: process.env.VITE_IA_PRESETID_GENCHARBOT || "mapp-gen-char-bot",
      iaVsPlayer: process.env.VITE_IA_PRESETID_IA_VS_PLAYER || "mapp-Claude_enygma_V2"
    }
  },
  google: {
    projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
    keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS
  }
};

// Inicializar clientes de Google Cloud
const speechClient = new speech.SpeechClient();
const ttsClient = new textToSpeech.TextToSpeechClient();

// Configuración para Speech-to-Text
const sttConfig = {
  encoding: 'WEBM_OPUS',
  sampleRateHertz: 48000,
  languageCode: 'es-ES',
  alternativeLanguageCodes: ['en-US'],
  enableAutomaticPunctuation: true,
  enableWordTimeOffsets: true,
  model: 'latest_long'
};

// Configuración para Text-to-Speech
const ttsConfig = {
  languageCode: 'es-ES',
  name: 'es-ES-Neural2-A',
  ssmlGender: 'FEMALE'
};

class AIBackend {
  constructor() {
    this.sessions = new Map(); // Almacenar sesiones activas
    this.app = uWS.App();
    this.setupWebSocketHandlers();
  }

  setupWebSocketHandlers() {
    this.app.ws('/*', {
      compression: uWS.SHARED_COMPRESSOR,
      maxCompressedSize: 64 * 1024,
      maxBackpressure: 64 * 1024,

      message: async (ws, message, opCode) => {
        try {
          const data = JSON.parse(Buffer.from(message).toString());
          await this.handleMessage(ws, data);
        } catch (error) {
          console.error('Error processing message:', error);
          this.sendError(ws, 'Invalid message format', error.message);
        }
      },

      open: (ws) => {
        const sessionId = uuidv4();
        ws.sessionId = sessionId;
        this.sessions.set(sessionId, {
          id: sessionId,
          iaSessionId: null,
          createdAt: new Date(),
          lastActivity: new Date()
        });

        console.log(`New WebSocket connection: ${sessionId}`);
        this.sendMessage(ws, {
          type: 'connection',
          status: 'connected',
          sessionId: sessionId
        });
      },

      close: (ws, code, message) => {
        if (ws.sessionId) {
          this.sessions.delete(ws.sessionId);
          console.log(`WebSocket connection closed: ${ws.sessionId}`);
        }
      }
    });

    // Endpoint para health check
    this.app.get('/health', (res, req) => {
      res.writeHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }));
    });
  }

  async handleMessage(ws, data) {
    const session = this.sessions.get(ws.sessionId);
    if (!session) {
      this.sendError(ws, 'Session not found');
      return;
    }

    session.lastActivity = new Date();

    switch (data.type) {
      case 'audio_chunk':
        await this.handleAudioChunk(ws, data, session);
        break;

      case 'audio_end':
        await this.processCompleteAudio(ws, session);
        break;

      case 'text_message':
        await this.handleTextMessage(ws, data, session);
        break;

      case 'set_preset':
        await this.handleSetPreset(ws, data, session);
        break;

      case 'reset_session':
        await this.handleResetSession(ws, session);
        break;

      default:
        this.sendError(ws, 'Unknown message type', data.type);
    }
  }

  async handleAudioChunk(ws, data, session) {
    try {
      // Inicializar buffer de audio si no existe
      if (!session.audioBuffer) {
        session.audioBuffer = [];
      }

      // Convertir base64 a buffer y agregar al buffer de audio
      const audioChunk = Buffer.from(data.audioData, 'base64');
      session.audioBuffer.push(audioChunk);

      this.sendMessage(ws, {
        type: 'audio_chunk_received',
        chunkSize: audioChunk.length
      });

    } catch (error) {
      console.error('Error handling audio chunk:', error);
      this.sendError(ws, 'Audio processing error', error.message);
    }
  }

  async processCompleteAudio(ws, session) {
    try {
      if (!session.audioBuffer || session.audioBuffer.length === 0) {
        this.sendError(ws, 'No audio data to process');
        return;
      }

      // Combinar todos los chunks de audio
      const completeAudio = Buffer.concat(session.audioBuffer);
      session.audioBuffer = []; // Limpiar buffer

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'speech_to_text'
      });

      // Procesar Speech-to-Text
      const transcription = await this.speechToText(completeAudio);

      if (!transcription || transcription.trim() === '') {
        this.sendError(ws, 'No speech detected in audio');
        return;
      }

      this.sendMessage(ws, {
        type: 'transcription',
        text: transcription
      });

      // Procesar con IA
      await this.processWithIA(ws, transcription, session);

    } catch (error) {
      console.error('Error processing complete audio:', error);
      this.sendError(ws, 'Audio processing failed', error.message);
    }
  }

  async handleTextMessage(ws, data, session) {
    try {
      if (!data.text || data.text.trim() === '') {
        this.sendError(ws, 'Empty text message');
        return;
      }

      await this.processWithIA(ws, data.text, session);

    } catch (error) {
      console.error('Error handling text message:', error);
      this.sendError(ws, 'Text processing failed', error.message);
    }
  }

  async handleSetPreset(ws, data, session) {
    try {
      const { preset } = data;

      if (!preset || !config.ia.presets[preset]) {
        this.sendError(ws, 'Invalid preset', `Available presets: ${Object.keys(config.ia.presets).join(', ')}`);
        return;
      }

      session.currentPreset = preset;
      session.iaSessionId = null; // Reset IA session when changing preset

      this.sendMessage(ws, {
        type: 'preset_set',
        preset: preset,
        presetId: config.ia.presets[preset]
      });

    } catch (error) {
      console.error('Error setting preset:', error);
      this.sendError(ws, 'Preset setting failed', error.message);
    }
  }

  async handleResetSession(ws, session) {
    try {
      if (session.iaSessionId) {
        // Reset IA session
        await this.resetIASession(session.iaSessionId);
      }

      session.iaSessionId = null;
      session.audioBuffer = [];
      session.currentPreset = null;

      this.sendMessage(ws, {
        type: 'session_reset',
        status: 'success'
      });

    } catch (error) {
      console.error('Error resetting session:', error);
      this.sendError(ws, 'Session reset failed', error.message);
    }
  }

  async speechToText(audioBuffer) {
    try {
      const request = {
        audio: {
          content: audioBuffer.toString('base64'),
        },
        config: sttConfig,
      };

      const [response] = await speechClient.recognize(request);
      const transcription = response.results
        .map(result => result.alternatives[0].transcript)
        .join('\n');

      console.log('Speech-to-Text result:', transcription);
      return transcription;

    } catch (error) {
      console.error('Speech-to-Text error:', error);
      throw new Error(`STT processing failed: ${error.message}`);
    }
  }

  async processWithIA(ws, text, session) {
    try {
      this.sendMessage(ws, {
        type: 'processing',
        stage: 'ai_processing'
      });

      // Usar preset por defecto si no se ha establecido uno
      const preset = session.currentPreset || 'genCharBot';
      const presetId = config.ia.presets[preset];

      // Preparar request para Model Gateway API
      const iaRequest = {
        id: {
          clt: ws.sessionId,
          ses: session.iaSessionId || undefined
        },
        preset: presetId,
        query: text
      };

      const response = await axios.post(
        `${config.ia.apiUrl}generate`,
        iaRequest,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': config.ia.apiKey
          }
        }
      );

      if (!response.data.ok) {
        throw new Error(response.data.message || 'IA processing failed');
      }

      // Actualizar session ID de la IA
      session.iaSessionId = response.data.id.ses;

      const iaResponse = response.data.output;

      this.sendMessage(ws, {
        type: 'ai_response',
        text: iaResponse,
        sessionId: session.iaSessionId,
        preset: preset
      });

      // Procesar Text-to-Speech
      await this.processTextToSpeech(ws, iaResponse);

    } catch (error) {
      console.error('IA processing error:', error);
      this.sendError(ws, 'IA processing failed', error.message);
    }
  }

  async processTextToSpeech(ws, text) {
    try {
      this.sendMessage(ws, {
        type: 'processing',
        stage: 'text_to_speech'
      });

      const request = {
        input: { text: text },
        voice: ttsConfig,
        audioConfig: { audioEncoding: 'MP3' },
      };

      const [response] = await ttsClient.synthesizeSpeech(request);
      const audioContent = response.audioContent.toString('base64');

      this.sendMessage(ws, {
        type: 'audio_response',
        audioData: audioContent,
        format: 'mp3'
      });

      console.log('Text-to-Speech completed');

    } catch (error) {
      console.error('Text-to-Speech error:', error);
      this.sendError(ws, 'TTS processing failed', error.message);
    }
  }

  async resetIASession(iaSessionId) {
    try {
      const response = await axios.get(
        `${config.ia.apiUrl}reset/${iaSessionId}`,
        {
          headers: {
            'X-Api-Key': config.ia.apiKey
          }
        }
      );

      console.log('IA session reset:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error resetting IA session:', error);
      throw error;
    }
  }

  sendMessage(ws, data) {
    try {
      ws.send(JSON.stringify(data));
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }

  sendError(ws, error, details = null) {
    this.sendMessage(ws, {
      type: 'error',
      error: error,
      details: details,
      timestamp: new Date().toISOString()
    });
  }

  // Cleanup de sesiones inactivas
  cleanupInactiveSessions() {
    const now = new Date();
    const maxInactiveTime = 30 * 60 * 1000; // 30 minutos

    for (const [sessionId, session] of this.sessions) {
      if (now - session.lastActivity > maxInactiveTime) {
        console.log(`Cleaning up inactive session: ${sessionId}`);
        this.sessions.delete(sessionId);
      }
    }
  }

  start() {
    // Iniciar cleanup periódico
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000); // Cada 5 minutos

    this.app.listen(config.port, (token) => {
      if (token) {
        console.log(`🚀 WebSocket server started on port ${config.port}`);
        console.log(`📡 Available endpoints:`);
        console.log(`   - WebSocket: ws://localhost:${config.port}`);
        console.log(`   - Health: http://localhost:${config.port}/health`);
        console.log(`🤖 IA Presets configured:`);
        Object.entries(config.ia.presets).forEach(([key, value]) => {
          console.log(`   - ${key}: ${value}`);
        });
      } else {
        console.error('❌ Failed to start server');
        process.exit(1);
      }
    });
  }
}

// Manejar cierre graceful del servidor
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

// Inicializar y arrancar el backend
const backend = new AIBackend();
backend.start();