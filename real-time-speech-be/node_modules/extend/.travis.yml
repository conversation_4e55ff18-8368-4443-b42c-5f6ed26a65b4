language: node_js
os:
 - linux
node_js:
  - "10.7"
  - "9.11"
  - "8.11"
  - "7.10"
  - "6.14"
  - "5.12"
  - "4.9"
  - "iojs-v3.3"
  - "iojs-v2.5"
  - "iojs-v1.8"
  - "0.12"
  - "0.10"
  - "0.8"
before_install:
  - 'case "${TRAVIS_NODE_VERSION}" in 0.*) export NPM_CONFIG_STRICT_SSL=false ;; esac'
  - 'nvm install-latest-npm'
install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ] || [ "${TRAVIS_NODE_VERSION}" = "0.9" ]; then nvm install --latest-npm 0.8 && npm install && nvm use "${TRAVIS_NODE_VERSION}"; else npm install; fi;'
script:
  - 'if [ -n "${PRETEST-}" ]; then npm run pretest ; fi'
  - 'if [ -n "${POSTTEST-}" ]; then npm run posttest ; fi'
  - 'if [ -n "${COVERAGE-}" ]; then npm run coverage ; fi'
  - 'if [ -n "${TEST-}" ]; then npm run tests-only ; fi'
sudo: false
env:
  - TEST=true
matrix:
  fast_finish: true
  include:
    - node_js: "lts/*"
      env: PRETEST=true
    - node_js: "lts/*"
      env: POSTTEST=true
    - node_js: "4"
      env: COVERAGE=true
    - node_js: "10.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "10.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "10.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "10.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "10.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "10.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "10.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.10"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "9.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.10"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "8.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "7.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.13"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.12"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.11"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.10"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "6.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.11"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.10"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "5.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.8"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "4.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v3.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v3.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v3.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v2.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.7"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.5"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.4"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.3"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.2"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.1"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "iojs-v1.0"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.11"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.9"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.6"
      env: TEST=true ALLOW_FAILURE=true
    - node_js: "0.4"
      env: TEST=true ALLOW_FAILURE=true
  allow_failures:
    - os: osx
    - env: TEST=true ALLOW_FAILURE=true
