{"version": 3, "file": "pathTemplate.js", "sourceRoot": "", "sources": ["../../src/pathTemplate.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAUH,MAAa,YAAY;IAKvB;;;;OAIG;IACH,YAAY,IAAY;QARhB,aAAQ,GAAa,EAAE,CAAC;QAS9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IACnC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,IAAY;QAChB,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,IAAI,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACjD,oEAAoE;YACpE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,SAAS,CACjB,aAAa,IAAI,iCAAiC,IAAI,CAAC,IAAI,yCAAyC,CACrG,CAAC;YACJ,CAAC;iBAAM,IAAI,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,IAAI,SAAS,CACjB,aAAa,IAAI,iCAAiC,IAAI,CAAC,IAAI,2DAA2D,CACvH,CAAC;YACJ,CAAC;QACH,CAAC;QACD,KACE,IAAI,KAAK,GAAG,CAAC,EACb,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EACvD,KAAK,EAAE,EACP,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxC,MAAM,IAAI,SAAS,CACjB,2BAA2B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,YAAY,CAAC,KAAK,CAAC,GAAG,CAC/E,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACnC,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;oBACzD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,MAAM,IAAI,KAAK,CACb,0CAA0C,OAAO,EAAE,CACpD,CAAC;oBACJ,CAAC;oBACD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAClC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAC1C,CAAC;oBACF,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;wBACjE,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACvC,CAAC;yBAAM,CAAC;wBACN,kBAAkB;wBAClB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC3B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC3C,CAAC;6BAAM,CAAC;4BACN,qBAAqB;4BACrB,6EAA6E;4BAC7E,6DAA6D;4BAC7D,4DAA4D;4BAC5D,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;4BAC9C,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;gCACtC,MAAM,IAAI,KAAK,CACb,WAAW,OAAO,mBAAmB,YAAY,CAAC,CAAC,CAAC,EAAE,CACvD,CAAC;4BACJ,CAAC;4BACD,KAAK,MAAM,CAAC,IAAI,SAAS,EAAE,CAAC;gCAC1B,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gCACvB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gCACrD,KAAK,CAAC,KAAK,EAAE,CAAC;4BAChB,CAAC;4BACD,yFAAyF;4BACzF,IAAI,OAAO,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gCAChC,MAAM,IAAI,SAAS,CACjB,8BAA8B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,YAAY,CAAC,CAAC,CAAC,6BAA6B,CACvG,CAAC;4BACJ,CAAC;wBACH,CAAC;wBACD,YAAY,CAAC,KAAK,EAAE,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,KAAK,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,QAAkB;QACvB,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;YACvE,MAAM,IAAI,SAAS,CACjB,2BACE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MACxB,kDACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAC7B,EAAE,CACH,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,IAAI,SAAS,CAAC,iCAAiC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAEpC,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC;gBACrB,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;oBACxB,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvB,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IACD;;;;;OAKG;IACK,iBAAiB,CAAC,IAAY;QACpC,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,IAAI,OAAgC,CAAC;QACrC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC7B,sCAAsC;YACtC,kCAAkC;YAClC,IAAI,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;gBACxC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,OAAO,CAAC;gBACrC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC;gBACxC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;gBAClB,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;oBACrB,EAAE,aAAa,CAAC;gBAClB,CAAC;YACH,CAAC;iBAAM,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,EAAE,CAAC;gBACvE,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;oBACjC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAChC,qCAAqC,CACtC,CAAC;oBACF,IAAI,CAAC,SAAS,EAAE,CAAC;wBACf,MAAM,IAAI,KAAK,CACb,wCAAwC,UAAU,EAAE,CACrD,CAAC;oBACJ,CAAC;oBACD,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,KAAK,GAAG,GAAG,CAAC;wBACZ,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;wBAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAC7B,CAAC;yBAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;wBACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAC7B,CAAC;yBAAM,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBAC1B,EAAE,aAAa,CAAC;wBAChB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBAC7B,CAAC;gBACH,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA3MD,oCA2MC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,IAAY;IACrC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,OAAO,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;YAC/B,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;YACtC,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;YACtC,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;YAChE,CAAC;YACD,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBACvB,+DAA+D;gBAC/D,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC3C,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QACD,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAC;YAC/C,CAAC;YACD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC"}