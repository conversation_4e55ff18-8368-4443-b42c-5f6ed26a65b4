{"version": 3, "file": "bundleDescriptor.js", "sourceRoot": "", "sources": ["../../../src/bundlingCalls/bundleDescriptor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,oEAA+D;AAE/D,uDAAkD;AAClD,qDAAgD;AAChD,kCAAwD;AAExD;;GAEG;AACH,MAAa,gBAAgB;IAM3B;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,YACE,YAAoB,EACpB,0BAAoC,EACpC,gBAA+B,EAC/B,kBAA4B;QAE5B,IAAI,CAAC,kBAAkB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE,CAAC;YAClE,kBAAkB,GAAG,gBAAgB,CAAC;YACtC,gBAAgB,GAAG,IAAI,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,0BAA0B;YAC7B,0BAA0B,CAAC,GAAG,CAAC,kBAAgB,CAAC,CAAC;QACnD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC/C,CAAC;IAED,YAAY,CAAC,QAAsB;QACjC,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO,IAAI,iCAAe,EAAE,CAAC;QAC/B,CAAC;QACD,OAAO,IAAI,iCAAe,CACxB,IAAI,+BAAc,CAAC,QAAQ,CAAC,aAAc,EAAE,IAAI,CAAC,CAClD,CAAC;IACJ,CAAC;CACF;AA1DD,4CA0DC"}