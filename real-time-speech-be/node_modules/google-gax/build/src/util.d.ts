/**
 * Converts a given string from camelCase (used by protobuf.js and in JSON)
 * to snake_case (normally used in proto definitions).
 */
export declare function camelToSnakeCase(str: string): string;
/**
 * Converts a given string from snake_case (normally used in proto definitions) or
 * PascalCase (also used in proto definitions) to camelCase (used by protobuf.js).
 * Preserves capitalization of the first character.
 */
export declare function toCamelCase(str: string): string;
/**
 * Converts a given string to lower camel case (forcing the first character to be
 * in lower case).
 */
export declare function toLowerCamelCase(str: string): string;
/**
 * Converts a given string to lower camel case (forcing the first character to be
 * in lower case).
 */
export declare function makeUUID(): string;
