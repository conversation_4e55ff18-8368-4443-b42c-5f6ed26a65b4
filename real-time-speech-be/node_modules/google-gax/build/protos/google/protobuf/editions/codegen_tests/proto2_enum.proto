// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto2";

package protobuf_editions_test.proto2;

enum Proto2Enum {
  BAR = 1;
  BAZ = 2;
}

message Proto2EnumMessage {
  optional Proto2Enum enum_field = 1;
  optional Proto2Enum enum_field_default = 2 [default = BAZ];
  enum Proto2NestedEnum {
    FOO = 1;
    BAT = 2;
  }
  optional Proto2NestedEnum nested_enum_field = 3;
  optional Proto2NestedEnum nested_enum_field_default = 4 [default = BAT];
}
