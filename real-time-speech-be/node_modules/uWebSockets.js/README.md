# uWebSockets.js precompiled binaries
Automatically built from source by GitHub Actions. The file "source_commit" contains the corresponding Git commit (SHA-1) that was built. As such you can validate the binaries by diffing against your own build of said commit (using the same compiler).

## Clarification of license

Files in this "binaries" branch are all licensed under Apache License 2.0, despite some of them lacking a notice as per "APPENDIX: How to apply the Apache License to your work" of the Apache License 2.0.
